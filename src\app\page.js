'use client';

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Dialog } from "@/components/ui/Dialog";
import { ArrowUpRight, Bell, GraduationCap } from "lucide-react";
import { useRouter } from "next/navigation";
import { toast } from 'sonner';

export default function Home() {
  const router = useRouter();

  return (
    <main className="flex flex-col gap-4 w-full h-screen items-center justify-center">
      <div className="element flex flex-col gap-4 w-full h-screen items-center justify-center">
        <button
          iconposition="right"
          className="flex text-5xl text-white items-center gap-2 p-4 rounded-xl"
          onClick={() => router.push('/login')}
        ><GraduationCap size={60} /> EduLearn </button>
        <div className="w-70 h-1.5 bg-gray-500 rounded overflow-hidden">
          <div className="h-full bg-white w-2/3 rounded"></div>
        </div>
        <div className="text-white">Loading...</div>
      </div>

    </main>
  );
}
